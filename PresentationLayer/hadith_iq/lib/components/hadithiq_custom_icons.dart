/// Flutter icons HadithIQCustomIcons
/// Copyright (C) 2025 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  HadithIQCustomIcons
///      fonts:
///       - asset: fonts/HadithIQCustomIcons.ttf
///
/// 
///
library;
import 'package:flutter/widgets.dart';

class HadithIQCustomIcons {
  HadithIQCustomIcons._();

  static const _kFontFam = 'HadithIQCustomIcons';
  static const String? _kFontPkg = null;

  static const IconData aiSearchFill = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData aiSearch = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
